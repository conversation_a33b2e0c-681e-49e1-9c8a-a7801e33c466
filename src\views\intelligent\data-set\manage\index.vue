<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="bg-w flex h-[60px] items-center pl-5 text-xl font-semibold">数据集管理</h2>

    <!-- 主体内容区 -->
    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <!-- 操作栏 -->
      <div class="flex justify-between px-10">
        <el-button type="primary" @click="onAdd">新增</el-button>
        <!-- <el-input
          v-model="search"
          placeholder="请输入数据集说明搜索"
          style="width: 300px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input> -->
      </div>

      <!-- 表格区域 -->
      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="datasetNameCn" min-width="100px" label="数据集名称(中文)" show-overflow-tooltip />
          <el-table-column prop="datasetName" min-width="100px" label="数据集名称(英文)" show-overflow-tooltip />
          <el-table-column prop="projectCode" label="课题编码缩写" />
          <el-table-column prop="description" label="数据集说明" show-overflow-tooltip />
          <el-table-column prop="diseaseTypeAnnotation" label="疾病类型" />
          <el-table-column prop="createDate" label="更新日期" width="170px" />
          <el-table-column prop="projectLeader" label="项目负责人" />
          <el-table-column prop="affiliatedUnit" label="所属单位" />
          <el-table-column prop="state" label="状态" />
          <el-table-column fixed="right" label="操作" width="200px">
            <template #default="{ row }">
              <div class="flex flex-wrap justify-center gap-y-2">
                <el-button link type="primary" @click="onViewDetail(row)">查看</el-button>
                <el-button v-if="row.state !== '废弃'" link type="primary" @click="onEdit(row)">修改</el-button>
                <el-popconfirm v-if="row.state !== '废弃'" title="确定停用？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary">停用</el-button>
                  </template>
                </el-popconfirm>
                <el-button v-if="row.state !== '废弃'" link type="primary" @click="onImport(row)">上传数据集</el-button>
                <el-button link type="primary" @click="onHistory(row)"> 历史数据集 </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <!-- 新增/编辑弹窗 -->
  <el-dialog
    v-model="showAdd"
    :title="formTitle"
    width="600px"
    destroy-on-close
    :close-on-press-escape="!uploadLoading"
    :close-on-click-modal="!uploadLoading"
    :before-close="handleAddDialogClose"
  >
    <div v-if="uploadLoading" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in logMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
      <div class="error-summary" v-if="hasErrors">
        <div class="error-title">
          <el-icon><WarningFilled /></el-icon> 错误信息汇总
        </div>
        <div class="error-list">
          <p v-for="(err, index) in errorMessages" :key="index">{{ err }}</p>
        </div>
      </div>
    </div>
    <DatasetForm
      v-else
      ref="datasetFormRef"
      show-database
      :show-metadata="showMetadata"
      @change-show-meatadata="(e) => (showMetadata = e)"
    />
    <template #footer>
      <span>
        <el-button @click="onAddClose">{{ uploadLoading ? '关闭' : '取消' }}</el-button>
        <el-button type="primary" :loading="addLoading" @click="onAddConfirm">{{
          uploadLoading ? '关闭' : '确定'
        }}</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 上传数据集弹窗 -->
  <el-dialog
    v-model="showDatabase"
    title="上传数据集"
    width="600px"
    :close-on-press-escape="!startUpload"
    :close-on-click-modal="!startUpload"
    :before-close="handleDatabaseDialogClose"
  >
    <div v-show="!startUpload">
      <el-form ref="dbFormRef" label-position="top" :model="dbForm" :rules="dbRules">
        <el-form-item label="数据集文件" prop="dbset">
          <el-upload v-model:file-list="dbForm.dbset" accept=".xls,.xlsx" :limit="1" :auto-upload="false">
            <el-button type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="startUpload" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in logMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
      <div class="error-summary" v-if="hasErrors">
        <div class="error-title">
          <el-icon><WarningFilled /></el-icon> 错误信息汇总
        </div>
        <div class="error-list">
          <p v-for="(err, index) in errorMessages" :key="index">{{ err }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <span>
        <el-button @click="onDatabaseClose">{{ startUpload ? '关闭' : '取消' }}</el-button>
        <el-button type="primary" :loading="addLoading" @click="onDatabaseConfirm">{{
          startUpload && uploadLoading ? '关闭' : '确定'
        }}</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 历史数据集弹窗 -->
  <HistoryDatasetDialog v-if="showHistory" v-model="showHistory" :current-row="currentRow!" />

  <VerifyDialog :id="verifyId" v-model="showVerify" @success="onVerifySuccess" />
</template>

<script setup lang="ts">
  // 导入所需的依赖
  import {
    findFileInforByUserId,
    deleteEntityById_36,
    findAllDb,
    newOrUpdateEntity_10,
    uploadFileExt,
    exportMedicalDataSetToDatabaseExt,
    getFileMessages,
    hasFileMessages,
  } from '@/api';
  import { WarningFilled } from '@element-plus/icons-vue';
  import { ElMessage, FormInstance, UploadFiles } from 'element-plus';
  import { useRouter } from 'vue-router';
  import DatasetForm from '../components/DatasetForm.vue';
  import VerifyDialog from '../components/VerifyDialog.vue';
  import dayjs from 'dayjs';
  import HistoryDatasetDialog from '../components/HistoryDatasetDialog.vue';
  import { useUsers } from '@/store/index';
  import { generateUUID } from '@/utils/crypto';
  const store = useUsers();

  // 基础数据
  const router = useRouter();
  const loading = ref(false);
  const currentRow = ref<FileInfoVO | null>(null);
  const importText = ref('');

  // 日志和错误处理
  interface LogMessage {
    text: string;
    isError: boolean;
  }
  const logMessages = ref<LogMessage[]>([]);
  const errorMessages = ref<string[]>([]);
  const hasErrors = computed(() => errorMessages.value.length > 0);

  // 表格数据
  const tableData = ref<FileInfoVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  // 新增/编辑表单相关
  const datasetFormRef = ref<any>(null);
  const showMetadata = ref(true);
  const showAdd = ref(false);
  const addLoading = ref(false);

  const formTitle = computed(() => {
    if (datasetFormRef.value) {
      return datasetFormRef.value.addForm.id ? '编辑数据集' : '新增数据集';
    }
    return '';
  });

  // 上传数据集相关
  const uploadLoading = ref(false);
  const showDatabase = ref(false);
  const databaseValue = ref(0);
  const databaseList = ref<CBDDefDatabaseVO[]>([]);
  const percentage = ref(0);
  const startUpload = ref(false);
  const dbFormRef = ref<FormInstance>();
  const dbForm = reactive({
    dbset: [] as UploadFiles,
  });
  const dbRules = ref({
    dbset: [{ required: true, message: '不能为空' }],
  });

  // 队列ID管理和进度查询
  const uploadQueueId = ref<string>('');
  const databaseQueueId = ref<string>('');
  const progressTimer = ref<NodeJS.Timeout | null>(null);

  // 清理进度查询定时器
  const clearProgressTimer = () => {
    if (progressTimer.value) {
      clearInterval(progressTimer.value);
      progressTimer.value = null;
    }
  };

  // 查询上传进度
  const queryUploadProgress = async (queueId: string) => {
    try {
      const { data } = await getFileMessages({ queueId });
      if (data && data.length > 0) {
        // 显示最新的进度信息
        data.forEach((message: string) => {
          addLogMessage(message);
        });

        // 检查是否完成
        const lastMessage = data[data.length - 1];
        if (lastMessage.includes('完成') || lastMessage.includes('成功') || lastMessage.includes('失败')) {
          clearProgressTimer();
          uploadLoading.value = true; // 标记为完成状态
          if (lastMessage.includes('成功') || lastMessage.includes('完成')) {
            ElMessage({ type: 'success', message: '上传完成' });
          } else if (lastMessage.includes('失败')) {
            ElMessage({ type: 'error', message: '上传失败' });
          }
        }
      }
    } catch (error) {
      console.error('查询进度失败:', error);
    }
  };

  // 检查是否有正在进行的上传任务
  const checkOngoingUpload = async () => {
    const storedUploadQueueId = localStorage.getItem('uploadQueueId');
    const storedDatabaseQueueId = localStorage.getItem('databaseQueueId');

    if (storedUploadQueueId) {
      try {
        const { data } = await hasFileMessages({ queueId: storedUploadQueueId });
        if (data) {
          uploadQueueId.value = storedUploadQueueId;
          showAdd.value = true;
          uploadLoading.value = true;
          addLogMessage('检测到正在进行的元数据上传任务，正在查询进度...');

          // 开始轮询进度
          progressTimer.value = setInterval(() => {
            queryUploadProgress(storedUploadQueueId);
          }, 2000);
        } else {
          localStorage.removeItem('uploadQueueId');
        }
      } catch (error) {
        console.error('检查上传进度失败:', error);
        localStorage.removeItem('uploadQueueId');
      }
    }

    if (storedDatabaseQueueId) {
      try {
        const { data } = await hasFileMessages({ queueId: storedDatabaseQueueId });
        if (data) {
          databaseQueueId.value = storedDatabaseQueueId;
          showDatabase.value = true;
          startUpload.value = true;
          addLogMessage('检测到正在进行的数据集上传任务，正在查询进度...');

          // 开始轮询进度
          progressTimer.value = setInterval(() => {
            queryUploadProgress(storedDatabaseQueueId);
          }, 2000);
        } else {
          localStorage.removeItem('databaseQueueId');
        }
      } catch (error) {
        console.error('检查数据集上传进度失败:', error);
        localStorage.removeItem('databaseQueueId');
      }
    }
  };

  // 校验数据相关
  const showVerify = ref(false);
  const verifyId = ref(0);

  // 添加日志消息的方法
  const addLogMessage = (text: string) => {
    const isError = text.includes('错误提示') || text.includes('失败');
    logMessages.value.push({ text, isError });

    // 如果是错误消息，添加到错误消息列表
    if (isError) {
      errorMessages.value.push(text);
    }

    // 保持滚动到最新消息
    nextTick(() => {
      const logContainer = document.querySelector('.log-messages');
      if (logContainer) {
        logContainer.scrollTop = logContainer.scrollHeight;
      }
    });
  };

  // 重置日志和错误信息
  const resetLogs = () => {
    logMessages.value = [];
    errorMessages.value = [];
    importText.value = '';
  };

  // 方法定义
  const fetchData = async (pageNum = 1) => {
    try {
      loading.value = true;
      const { data } = await findFileInforByUserId(store.user.id, pageNum, pagination.pageSize);
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchData(page);
  };

  const onAdd = () => {
    resetLogs();
    showMetadata.value = true;
    showAdd.value = true;
  };

  const onEdit = async (row: FileInfoVO) => {
    resetLogs();
    showMetadata.value = false;
    showAdd.value = true;
    nextTick(() => {
      if (row) {
        Object.keys(datasetFormRef.value.addForm).forEach((key) => {
          if (datasetFormRef.value.addForm[key] !== null && datasetFormRef.value.addForm[key] !== undefined) {
            datasetFormRef.value.addForm[key] = row[key];
          }
        });
      }
    });
  };

  // 处理新增弹窗关闭
  const handleAddDialogClose = (done: () => void) => {
    if (uploadLoading.value) {
      ElMessage({ type: 'warning', message: '上传正在进行中，关闭弹窗不会取消上传进度' });
      done();
    } else {
      onAddClose();
      done();
    }
  };

  const onAddClose = () => {
    clearProgressTimer();
    addLoading.value = false;
    showAdd.value = false;
    datasetFormRef.value?.formRef?.resetFields();
    uploadLoading.value = false;
    resetLogs();
    // 只有在非上传状态下才清理队列ID
    if (!uploadLoading.value && uploadQueueId.value) {
      localStorage.removeItem('uploadQueueId');
      uploadQueueId.value = '';
    }
  };

  const onAddConfirm = async () => {
    if (uploadLoading.value) {
      onAddClose();
      return;
    }

    if (!datasetFormRef.value.formRef) {
      ElMessage({ type: 'warning', message: '请先导入元数据' });
      return;
    }

    const valid = await datasetFormRef.value.formRef.validate();
    if (!valid) return;

    try {
      addLoading.value = true;
      resetLogs();

      if (!datasetFormRef.value.addForm.id) {
        uploadLoading.value = true;
        // 新增 - 使用新的API接口
        const databaseId = datasetFormRef.value.addForm.databaseId;
        const queueId = generateUUID();
        uploadQueueId.value = queueId;

        // 保存队列ID到本地存储
        localStorage.setItem('uploadQueueId', queueId);

        addLogMessage('开始上传元数据...');

        // 准备表单数据
        const formData = new FormData();
        const fileInforDTO = {
          ...datasetFormRef.value.addForm,
          createDate: dayjs(datasetFormRef.value.addForm.createDate).format('YYYY-MM-DD'),
          id: null,
        };
        delete fileInforDTO.databaseId; // 移除databaseId字段
        formData.append('fileInforDTO', new Blob([JSON.stringify(fileInforDTO)], { type: 'application/json' }));

        // 调用新的上传接口
        await uploadFileExt(formData, {
          cbdDatabaseId: databaseId,
          userIdCur: store.user.id,
          queueId: queueId,
        });

        addLogMessage('上传请求已提交，正在处理...');

        // 开始轮询进度
        progressTimer.value = setInterval(() => {
          queryUploadProgress(queueId);
        }, 2000);
      } else {
        // 编辑
        const form = {
          ...datasetFormRef.value.addForm,
          createDate: dayjs(datasetFormRef.value.addForm.createDate).format('YYYY-MM-DD'),
        };
        await newOrUpdateEntity_10(form);
        onAddClose();
        ElMessage({ type: 'success', message: '操作成功' });
        fetchData();
      }
    } catch (error) {
      console.error(error);
      ElMessage({ type: 'error', message: '上传失败' });
    } finally {
      addLoading.value = false;
    }
  };

  const onViewDetail = (row: FileInfoVO) => {
    router.push({ name: 'DatasetField', params: { id: row.id } });
  };

  const onDel = async (row: FileInfoVO) => {
    try {
      loading.value = true;
      await deleteEntityById_36(row.id!);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const onVerifySuccess = () => {
    fetchDatabase();
    databaseValue.value = 0;
    showDatabase.value = true;
  };

  const onImport = (row: FileInfoVO) => {
    resetLogs();
    currentRow.value = row;
    showDatabase.value = true;
  };

  const showHistory = ref(false);
  const onHistory = (row: FileInfoVO) => {
    currentRow.value = row;
    showHistory.value = true;
  };

  // 处理数据库弹窗关闭
  const handleDatabaseDialogClose = (done: () => void) => {
    if (startUpload.value) {
      ElMessage({ type: 'warning', message: '上传正在进行中，关闭弹窗不会取消上传进度' });
      done();
    } else {
      onDatabaseClose();
      done();
    }
  };

  const onDatabaseClose = () => {
    clearProgressTimer();
    startUpload.value = false;
    uploadLoading.value = false; // 重置上传加载状态
    dbFormRef.value?.resetFields();
    showDatabase.value = false;
    addLoading.value = false;
    resetLogs();
    // 只有在非上传状态下才清理队列ID
    if (!startUpload.value && databaseQueueId.value) {
      localStorage.removeItem('databaseQueueId');
      databaseQueueId.value = '';
    }
  };

  const fetchDatabase = async () => {
    try {
      const { data } = await findAllDb();
      databaseList.value = data!;
    } catch (error) {
      console.error(error);
    }
  };

  const onDatabaseConfirm = async () => {
    if (uploadLoading.value) {
      onDatabaseClose();
      return;
    }

    const valid = await dbFormRef.value?.validate();
    if (!valid) return;

    try {
      addLoading.value = true;
      startUpload.value = true;
      percentage.value = 0;
      resetLogs();

      const queueId = generateUUID();
      databaseQueueId.value = queueId;

      // 保存队列ID到本地存储
      localStorage.setItem('databaseQueueId', queueId);

      addLogMessage('开始上传数据集...');

      // 准备表单数据
      const formData = new FormData();
      formData.append('datasetFile', dbForm.dbset[0].raw!);

      // 调用新的数据集导入接口
      await exportMedicalDataSetToDatabaseExt(formData, {
        fileId: currentRow.value?.id || 0,
        queueId: queueId,
      });

      addLogMessage('上传请求已提交，正在处理...');

      // 开始轮询进度
      progressTimer.value = setInterval(() => {
        queryUploadProgress(queueId);
      }, 2000);
    } catch (error) {
      console.error('数据库导入失败:', error);
      ElMessage({ type: 'error', message: '上传失败' });
    } finally {
      addLoading.value = false;
    }
  };

  // 生命周期钩子
  onBeforeMount(() => {
    fetchData();
    // 检查是否有正在进行的上传任务
    checkOngoingUpload();
  });

  onUnmounted(() => {
    clearProgressTimer();
    startUpload.value = false;
  });
</script>

<style lang="scss" scoped>
  .vertical-radio-group {
    display: block;
    .el-radio {
      display: block;
    }
  }

  .log-container {
    display: flex;
    flex-direction: column;
    height: 400px;
    width: 100%;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
  }

  .log-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    font-family: monospace;
    background-color: #f8f9fa;
    line-height: 1.5;
    font-size: 14px;
  }

  .error-message {
    color: #f56c6c;
    font-weight: 500;
  }

  .error-summary {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff0f0;
    border-top: 1px solid #fde2e2;
  }

  .error-title {
    display: flex;
    align-items: center;
    color: #f56c6c;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;

    .el-icon {
      margin-right: 6px;
    }
  }

  .error-list {
    max-height: 100px;
    overflow-y: auto;

    p {
      margin: 4px 0;
      padding-left: 16px;
      font-size: 13px;
      color: #f56c6c;
    }
  }
</style>
