# 数据集管理页面修改说明

## 修改概述

将数据集管理页面从使用SSE（Server-Sent Events）方式改为使用新的API接口进行数据上传，并实现了上传进度查询功能。

## 主要修改内容

### 1. API接口更新

#### 新增API接口
- `uploadFileExt`: 导入Excel中的数据资源清单和医学数据的元数据
- `exportMedicalDataSetToDatabaseExt`: 把Excel医学数据集中的医学数据导入数据库
- `getFileMessages`: 医学数据集元数据和医学数据数据导入处理进度查询
- `hasFileMessages`: 检查是否有正在进行的上传任务

#### 接口参数说明
- `queueId`: 消息队列代码，建议使用UUID保证唯一性
- `cbdDatabaseId`: 数据库ID
- `userIdCur`: 当前用户ID
- `fileId`: 文件ID

### 2. 队列ID管理

#### 新增变量
```typescript
const uploadQueueId = ref<string>('');        // 元数据上传队列ID
const databaseQueueId = ref<string>('');      // 数据集上传队列ID
const progressTimer = ref<NodeJS.Timeout | null>(null); // 进度查询定时器
```

#### 本地存储
- 上传开始时将队列ID保存到localStorage
- 页面加载时检查是否有正在进行的上传任务
- 上传完成或取消时清理localStorage

### 3. 进度查询功能

#### 轮询机制
- 每2秒查询一次上传进度
- 显示实时进度信息
- 自动检测上传完成状态

#### 状态判断
- 包含"完成"、"成功"：上传成功
- 包含"失败"：上传失败
- 其他：上传进行中

### 4. 弹窗关闭逻辑优化

#### 防止意外取消
- 上传进行中时，关闭弹窗不会取消上传进程
- 弹窗关闭时显示提示信息
- 按钮文本动态变化（"取消"/"关闭"）

#### 弹窗配置
```typescript
:close-on-press-escape="!uploadLoading"
:close-on-click-modal="!uploadLoading"
:before-close="handleAddDialogClose"
```

### 5. 上传流程改进

#### 元数据上传流程
1. 生成UUID作为队列ID
2. 保存队列ID到localStorage
3. 调用`uploadFileExt`接口
4. 开始轮询进度
5. 显示进度信息
6. 完成后清理资源

#### 数据集上传流程
1. 生成UUID作为队列ID
2. 准备FormData（包含文件）
3. 调用`exportMedicalDataSetToDatabaseExt`接口
4. 开始轮询进度
5. 显示进度信息
6. 完成后清理资源

### 6. 错误处理

#### 异常情况处理
- 网络错误
- 接口调用失败
- 进度查询失败
- 上传中断

#### 用户提示
- 成功提示
- 错误提示
- 进度提示
- 状态变化提示

### 7. 生命周期管理

#### 页面加载时
```typescript
onBeforeMount(() => {
  fetchData();
  checkOngoingUpload(); // 检查正在进行的上传任务
});
```

#### 页面卸载时
```typescript
onUnmounted(() => {
  clearProgressTimer(); // 清理定时器
  startUpload.value = false;
});
```

## 技术要点

### 1. UUID生成
使用`generateUUID()`函数生成唯一的队列ID

### 2. 定时器管理
- 使用`setInterval`进行轮询
- 及时清理定时器避免内存泄漏

### 3. 本地存储
- 使用localStorage持久化队列ID
- 页面刷新后能恢复上传状态

### 4. 文件上传
- 使用FormData传递文件数据
- 使用upload函数处理multipart/form-data

## 用户体验改进

1. **上传不中断**: 关闭弹窗不会取消上传进度
2. **状态恢复**: 页面刷新后能显示正在进行的上传
3. **实时反馈**: 显示详细的上传进度信息
4. **错误处理**: 完善的错误提示和处理机制
5. **界面优化**: 按钮文本和状态的动态变化

## 注意事项

1. 确保后端API接口支持队列ID参数
2. 进度查询接口需要返回详细的状态信息
3. 队列ID需要在前后端保持一致
4. 定时器需要及时清理避免内存泄漏
5. 本地存储的队列ID需要在适当时机清理
